# 地图实例切换功能修复说明

## 问题分析

之前的实现中，地图实例的销毁和重建没有效果，主要原因是：

1. **fdapi 变量管理问题**：`fdapi` 是一个全局变量，需要通过 `api.value.getAPI()` 获取，但之前没有正确管理这个变量的生命周期。

2. **地图实例创建方式问题**：`DigitalTwinPlayer` 的构造函数不应该使用 `await`，它是同步的。

3. **API 引用问题**：销毁和重建过程中没有正确清理和重新设置 `fdapi` 引用。

## 修复内容

### 1. 添加 fdapi 变量管理
```javascript
// 全局 fdapi 变量
let fdapi = null
```

### 2. 修复 onReady 回调
```javascript
onReady: function () {
  // 获取 API 实例并设置为全局变量
  fdapi = api.value.getAPI()
  fdapi.settings.setMainUIVisibility(false)
  init2DWater()
}
```

### 3. 修复销毁函数
```javascript
const destroyRootMapInstance = () => {
  // 清除所有地图相关的数据和标记
  if (fdapi) {
    fdapi.marker.clear()
    fdapi.weather.disableRainSnow()
    fdapi.hydrodynamic2d.clear()
    fdapi.geoJSONLayer.clear()
  }

  // 销毁地图实例
  if (api.value) {
    if (typeof api.value.destroy === 'function') {
      api.value.destroy()
    }
    api.value = null
  }

  // 清空 fdapi 引用
  fdapi = null
}
```

### 4. 修复重建函数
```javascript
const recreateRootMapInstance = async () => {
  // 重新创建数字孪生平台实例（不使用 await）
  api.value = new DigitalTwinPlayer(host.value, options.value)
  
  // 等待 fdapi 初始化完成
  await new Promise((resolve) => {
    const checkReady = () => {
      if (fdapi) {
        resolve()
      } else {
        setTimeout(checkReady, 100)
      }
    }
    checkReady()
  })
}
```

### 5. 修复切换逻辑
```javascript
const changeNav = index => {
  const previousIndex = activeNavButton.value

  // 只有从辅助决策页面切换到其他页面时才重建
  if (previousIndex === 3 && index !== 3) {
    recreateRootMapInstance()
  }

  // 切换到辅助决策页面时销毁
  if (index === 3) {
    destroyRootMapInstance()
  }

  // 只有在非辅助决策页面且 fdapi 存在时才执行地图操作
  if (index !== 3 && fdapi) {
    // 地图相关操作...
  }
}
```

## 测试步骤

### 1. 初始状态测试
- 页面加载后应该显示综合展示页面
- 控制台应该输出："加载飞渡" 和 "数字孪生平台初始化成功"
- 地图应该正常显示，`fdapi` 应该不为 null

### 2. 切换到辅助决策测试
- 点击"辅助决策"导航按钮
- 控制台应该输出："切换到辅助决策页面，销毁rootMap地图实例"
- 控制台应该输出："开始销毁rootMap地图实例" 和 "rootMap地图实例销毁完成"
- rootMap 地图容器应该隐藏
- RollupEffect 组件应该显示

### 3. 切换回其他页面测试
- 从辅助决策页面点击其他导航按钮
- 控制台应该输出："从辅助决策页面切换到其他页面，重新创建rootMap地图实例"
- 控制台应该输出："开始重新创建rootMap地图实例" 和 "重新加载飞渡地图"
- 控制台应该输出："rootMap地图实例重新创建成功"
- 3秒后应该输出："管网加载-排水分区重新加载完成"
- rootMap 地图应该重新显示并正常工作

### 4. 验证地图功能
- 切换回来后，地图的所有功能应该正常工作
- 标记点、图层、交互等功能应该正常
- `fdapi` 应该重新可用

## 关键改进

1. **正确的生命周期管理**：`fdapi` 变量现在有正确的创建、使用和清理流程
2. **异步处理**：重建过程中正确等待 `fdapi` 初始化完成
3. **错误处理**：所有关键操作都有适当的错误处理
4. **资源清理**：确保在销毁时完全清理所有资源

现在地图实例的销毁和重建应该能够正常工作了！
